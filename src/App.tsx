import AppRouter from '@/routes'
import <PERSON><PERSON><PERSON><PERSON> from 'react-cache-buster'
import AppProvider from './contexts/AppProvider'
import { version } from '../package.json'
import Loading from './components/Loading'
import InstallPrompt from '@/core/components/pwa-install/InstallPrompt'

export default function App() {
  // return (
  //   <CacheBuster
  //     currentVersion={version}
  //     isEnabled
  //     isVerboseMode={false} //If true, the library writes verbose logs to console.
  //     loadingComponent={<Loading />} //If not pass, nothing appears at the time of new version check.
  //   >
  //     <AppProvider>
  //       <AppRouter />
  //     </AppProvider>
  //   </CacheBuster>
  // )

  return (
    <AppProvider>
      <AppRouter />
      <InstallPrompt />
    </AppProvider>
  )
}
