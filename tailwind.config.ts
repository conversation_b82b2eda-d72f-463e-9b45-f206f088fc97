import type { Config } from 'tailwindcss'
import logical from 'tailwindcss-logical'
import animate from 'tailwindcss-animate'
import equalindoPlugin from './src/core/tailwind/plugin'

const config: Config = {
  content: ['./pages/**/*.{ts,tsx}', './components/**/*.{ts,tsx}', './app/**/*.{ts,tsx}', './src/**/*.{ts,tsx}'],
  corePlugins: {
    preflight: false
  },
  important: 'body',
  plugins: [logical, equalindoPlugin, animate],
  theme: {
    extend: {
      fontSize: {
        xxs: '.6rem'
      }
    }
  }
}

export default config
