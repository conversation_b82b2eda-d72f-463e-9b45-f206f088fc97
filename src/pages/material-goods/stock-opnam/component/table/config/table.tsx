import NumberField from '@/components/numeric/NumberField'
import { SoItemType, SoPayload } from '@/types/soTypes'
import { isNullOrUndefined } from '@/utils/helper'
import { FormControl, IconButton, InputLabel, MenuItem, Select, TextField, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { Control, Controller, UseFormGetValues, useWatch } from 'react-hook-form'

type SoItemTypeWithActions = SoItemType & {
  deleteAction?: string
}

type RowActionType = {
  delete: (idx: number, id: string) => void
}

const columnHelper = createColumnHelper<SoItemTypeWithActions>()

export const editableTableColumns = (
  control: Control<SoPayload>,
  getValues: UseFormGetValues<SoPayload>,
  rowAction: RowActionType
) => [
  columnHelper.accessor('item.name', {
    header: 'Nama Item',
    cell: ({ row }) => (
      <div className='flex flex-col'>
        <Typography>{row.original.item?.name}</Typography>
        <Typography variant='caption'>
          {row.original.item?.brandName} - {row.original.item?.number}
        </Typography>
      </div>
    ),
    enableSorting: false
  }),
  columnHelper.accessor('systemStock', {
    header: 'Total Stok',
    cell: ({ row }) => (
      <Typography>
        {row.original.systemStock} {row.original.item?.smallUnit}
      </Typography>
    ),
    enableSorting: false
  }),
  columnHelper.accessor('relationedStock', {
    header: 'Stok Relasi',
    cell: ({ row }) => (
      <Typography>
        {row.original.relationedStock} {row.original.item?.smallUnit}
      </Typography>
    ),
    enableSorting: false
  }),
  columnHelper.accessor('actualStock', {
    header: 'Stok Aktual',
    cell: ({ row }) => {
      const { item } = row.original
      const isLargeUnit = useWatch({
        control,
        name: `items.${row.index}.isLargeUnit`
      })
      return (
        <Controller
          name={`items.${row.index}.actualStock`}
          control={control}
          render={({ field, fieldState: { error } }) => {
            return (
              <TextField
                {...field}
                value={field.value}
                className='w-24'
                variant='outlined'
                InputProps={{
                  endAdornment: isLargeUnit ? item.largeUnit : item.smallUnit,
                  inputComponent: NumberField as any,
                  inputProps: {
                    isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined
                  }
                }}
                {...(error && { error: true, helperText: 'Wajib diisi.' })}
              />
            )
          }}
        />
      )
    },
    enableSorting: false
  }),
  columnHelper.accessor('isLargeUnit', {
    header: 'Satuan',
    cell: ({ row }) => {
      const { item } = row.original
      return (
        <Controller
          name={`items.${row.index}.isLargeUnit`}
          control={control}
          render={({ field: { value, onChange } }) => {
            return (
              <Select
                id='isLargeUnit'
                placeholder='Pilih Satuan'
                className='bg-white'
                fullWidth
                value={value ? item.largeUnit : item.smallUnit}
                onChange={e => onChange(e.target.value === item.largeUnit)}
              >
                <MenuItem key={item?.largeUnit} value={item?.largeUnit}>
                  {item?.largeUnit}
                </MenuItem>
                <MenuItem key={item?.smallUnit} value={item?.smallUnit}>
                  {item?.smallUnit}
                </MenuItem>
              </Select>
            )
          }}
        />
      )
    },
    enableSorting: false
  }),
  columnHelper.accessor('differenceStock', {
    header: 'Selisih',
    cell: ({ row }) => {
      const actualStock = useWatch({
        control,
        name: `items.${row.index}.actualStock`
      })
      const isLargeUnit = useWatch({
        control,
        name: `items.${row.index}.isLargeUnit`
      })
      if (isNullOrUndefined(actualStock)) return <Typography>-</Typography>
      const actualStockValue = isLargeUnit ? actualStock * (row.original.item?.largeUnitQuantity ?? 1) : actualStock
      const rem = row.original.differenceStock
        ? row.original.differenceStock
        : (actualStockValue ?? 0) - row.original.systemStock
      const color = rem < 0 ? 'text-red-500' : rem > 0 ? 'text-green-500' : ''
      const remStr = rem > 0 ? `+${rem}` : `${rem}`
      return (
        <Typography className={`${color}`}>
          {remStr} {row.original.item?.smallUnit}
        </Typography>
      )
    },
    enableSorting: false
  }),
  columnHelper.accessor('note', {
    header: 'Catatan',
    cell: ({ row }) => {
      const value = useWatch({
        control,
        name: 'items'
      })
      return (
        <Controller
          name={`items.${row.index}.note`}
          control={control}
          render={({ field, fieldState: { error } }) => (
            <TextField
              {...field}
              value={value?.[row.index].note ?? ''}
              size='small'
              fullWidth
              variant='outlined'
              placeholder='Contoh: barang hilang/rusak'
              {...(error && { error: true, helperText: 'Wajib diisi.' })}
            />
          )}
        />
      )
    },
    enableSorting: false
  }),
  columnHelper.accessor('deleteAction', {
    header: '',
    cell: ({ row }) => (
      <IconButton size='medium' onClick={() => rowAction.delete(row.index, row.original.itemId)}>
        <i className='ic-baseline-delete-forever text-red-500' />
      </IconButton>
    ),
    enableSorting: false
  })
]

export const tableColumns = () => [
  columnHelper.accessor('item.name', {
    header: 'Nama Item',
    cell: ({ row }) => (
      <div className='flex flex-col'>
        <Typography>{row.original.item?.name}</Typography>
        <Typography variant='caption'>
          {row.original.item?.brandName} - {row.original.item?.number}
        </Typography>
      </div>
    )
  }),
  columnHelper.accessor('systemStock', {
    header: 'Stok Sistem',
    cell: ({ row }) => (
      <Typography>
        {row.original.systemStock} {row.original.item?.smallUnit}
      </Typography>
    )
  }),
  columnHelper.accessor('actualStock', {
    header: 'Stok Aktual',
    cell: ({ row }) => (
      <Typography>
        {row.original.actualStock} {row.original.item?.smallUnit}
      </Typography>
    )
  }),
  columnHelper.accessor('differenceStock', {
    header: 'Selisih',
    cell: ({ row }) => {
      const rem = row.original.differenceStock
      const remStr = rem > 0 ? `+${rem}` : `${rem}`
      return (
        <Typography>
          {remStr} {row.original.item?.smallUnit}
        </Typography>
      )
    }
  }),
  columnHelper.accessor('note', {
    header: 'Catatan',
    cell: ({ row }) => <Typography>{row.original.note}</Typography>,
    enableSorting: false
  })
]
