import {
  <PERSON>complete,
  But<PERSON>,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  debounce,
  FormHelperText,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useMemo, useState } from 'react'
import DialogItems from './dialog-items'
import { Controller, useFieldArray, useFormContext, useWatch } from 'react-hook-form'
import { CreatePaymentItemPayload, CreatePaymentPayload } from '../../config/types'
import AddPurchaseInvoiceDialog from '@/components/dialogs/add-purchase-invoice-dialog'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import { isNullOrUndefined, toCurrency } from '@/utils/helper'
import { PurchaseInvoice, PurchaseInvoiceStatus } from '@/types/purchaseInvoiceTypes'
import { useQuery } from '@tanstack/react-query'
import PurchaseInvoiceQueryMethods, {
  PURCHASE_INVOICE_DETAIL_QUERY_KEY,
  PURCHASE_INVOICE_LIST_QUERY_KEY
} from '@/api/services/purchase-invoice/query'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useSearchParams } from 'react-router-dom'
import { statusChipValue } from '@/pages/purchase-invoice/detail/components/ApprovalsCard'

const ItemListCard = () => {
  const [searchParams] = useSearchParams()
  const [dialogItem, setDialogItem] = useState<boolean>(false)
  const [editedItem, setEditedItem] = useState<{ index: number; item: CreatePaymentItemPayload } | null>(null)
  const [selectedInvoice, setSelectedInvoice] = useState<PurchaseInvoice[]>([])
  const [invoiceSearchQuery, setInvoiceSearchQuery] = useState('')
  const { control, reset, getValues, setValue } = useFormContext<CreatePaymentPayload>()
  const { remove, fields, append } = useFieldArray({ control, name: 'items' })
  const typeWatch = useWatch({ control, name: 'type' })
  const [openAddPurchaseInvoice, setOpenAddPurchaseInvoice] = useState(false)

  const purchaseInvoiceIdsWatch = useWatch({ control, name: 'purchaseInvoiceIds' })

  const { data: purchaseInvoiceDetail } = useQuery({
    enabled: !!searchParams.get('purchaseInvoiceId'),
    queryKey: [PURCHASE_INVOICE_DETAIL_QUERY_KEY, searchParams.get('purchaseInvoiceId')],
    queryFn: () => PurchaseInvoiceQueryMethods.getPurchaseInvoice(searchParams.get('purchaseInvoiceId') as string)
  })

  useEffect(() => {
    if (purchaseInvoiceDetail) {
      setSelectedInvoice([purchaseInvoiceDetail])
      setValue('purchaseInvoiceIds', [purchaseInvoiceDetail.id])
    }
  }, [purchaseInvoiceDetail])

  const tableOptions: any = useMemo(
    () => ({
      data: fields ?? [],
      columns: tableColumns({
        delete: index => remove(index),
        edit: index => {
          setEditedItem({ index, item: fields[index] as CreatePaymentItemPayload })
          setDialogItem(true)
        }
      }),
      initialState: {
        pagination: {
          pageSize: 10,
          pageIndex: 0
        }
      },
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [fields, remove]
  )

  const table = useReactTable(tableOptions)

  const handleAddItem = (item: CreatePaymentItemPayload) => {
    if (editedItem) {
      const newFields = [...fields]
      newFields[editedItem.index] = { ...newFields[editedItem.index], ...item }
      setValue('items', newFields)
    } else {
      append(item)
    }
    setValue('siteId', item?.siteId)
    setValue('departmentId', item?.departmentId)
    setDialogItem(false)
    setEditedItem(null)
  }

  const removePurchaseInvoice = () => {
    reset({
      ...getValues(),
      purchaseInvoiceIds: []
    })
    setSelectedInvoice([])
  }

  useEffect(() => {
    if (typeWatch !== 'PURCHASE') {
      removePurchaseInvoice()
    }
  }, [typeWatch])

  useEffect(() => {
    if (selectedInvoice?.length > 0) {
      setValue('siteId', selectedInvoice?.[0]?.siteId)
      setValue('departmentId', selectedInvoice?.[0]?.departmentId)
      setValue('projectLabelId', selectedInvoice?.[0]?.projectLabelId)
      setValue('unitId', selectedInvoice?.[0]?.unitId)
      setValue('unit', selectedInvoice?.[0]?.unit)
    }
  }, [selectedInvoice])

  if (typeWatch === 'GENERAL') {
    return (
      <>
        <Card>
          <CardContent className='flex flex-col gap-4'>
            <div className='flex justify-between'>
              <Typography variant='h5'>List Item</Typography>
              <Button onClick={() => setDialogItem(true)} variant='outlined'>
                Tambah Item
              </Button>
            </div>
            <div>
              {fields?.length > 0 ? (
                <div className='flex flex-col gap-4'>
                  <div className='rounded-[8px] shadow-md'>
                    <Table table={table} disablePagination headerColor='green' />
                  </div>
                  <div className='flex flex-col gap-1 rounded-md p-3 bg-[#DBF7E8]'>
                    <small>Total Pembayaran</small>
                    <Typography color='primary'>
                      {toCurrency(fields.reduce((acc, item) => acc + item.amount, 0))}
                    </Typography>
                  </div>
                </div>
              ) : (
                <Controller
                  control={control}
                  name='items'
                  rules={{ required: true }}
                  render={({ fieldState: { error } }) => (
                    <>
                      <div className='flex flex-col justify-center items-center gap-2'>
                        <Typography variant='h5'>Belum ada item</Typography>
                        <Typography variant='body1'>
                          Tambahkan detil item yang harus dibayar dengan tombol diatas
                        </Typography>
                      </div>
                      {!!error && <FormHelperText error>Wajib diisi.</FormHelperText>}
                    </>
                  )}
                />
              )}
            </div>
          </CardContent>
        </Card>
        {dialogItem && (
          <DialogItems
            item={editedItem?.item}
            onSubmit={handleAddItem}
            open={dialogItem}
            setOpen={val => {
              setDialogItem(val)
              setEditedItem(null)
            }}
          />
        )}
      </>
    )
  }
  if (typeWatch === 'PURCHASE') {
    return (
      <>
        <Card>
          <CardContent className='flex flex-col gap-4'>
            <div className='flex justify-between'>
              <Typography variant='h5'>Pembayaran Untuk Faktur</Typography>
              <Button variant='outlined' onClick={() => setOpenAddPurchaseInvoice(true)}>
                Pilih Faktur
              </Button>
            </div>
            {selectedInvoice.length > 0 ? (
              selectedInvoice.map(invoice => (
                <div key={invoice.id}>
                  <div className='flex justify-between gap-2 p-4 rounded-[8px] bg-[#4C4E640D]'>
                    <div className='flex flex-col gap-2'>
                      <div className='flex gap-2 items-center'>
                        <Typography variant='h5'>No. Faktur: {invoice?.number}</Typography>
                        <Chip
                          label={statusChipValue[invoice?.status]?.label}
                          size='small'
                          variant='tonal'
                          color={statusChipValue[invoice?.status]?.color}
                        />
                      </div>
                      <small>{formatDate(invoice?.createdAt, 'eeee, dd/MM/yyyy', { locale: id })}</small>
                    </div>
                    <IconButton
                      onClick={() => {
                        const newSelectedInvoices = selectedInvoice.filter(i => i.id !== invoice.id)
                        setSelectedInvoice(newSelectedInvoices)
                        setValue(
                          'purchaseInvoiceIds',
                          newSelectedInvoices.map(i => i.id)
                        )
                      }}
                    >
                      <i className='ri-close-circle-line' />
                    </IconButton>
                  </div>
                  <div className='grid grid-cols-2 md:grid-cols-3 gap-2 mt-2'>
                    <div className='flex flex-col gap-2 p-4 rounded-[8px] bg-[#4C4E640D]'>
                      <small>Sub Total Faktur</small>
                      <Typography className='font-semibold'>
                        {toCurrency(invoice?.subTotalAmount, false, invoice?.currency?.code)}
                      </Typography>
                    </div>
                    <div className='flex flex-col gap-2 p-4 rounded-[8px] bg-[#4C4E640D]'>
                      <small>Biaya Lain Lain</small>
                      <Typography className='font-semibold'>
                        {toCurrency(invoice?.otherAmount, false, invoice?.currency?.code)}
                      </Typography>
                    </div>
                    <div className='flex flex-col gap-2 p-4 rounded-[8px] bg-[#DBF7E8]'>
                      <small>Total Faktur</small>
                      <Typography variant='h6' color='black' className='font-semibold'>
                        {toCurrency(invoice?.totalAmount, false, invoice?.currency?.code)}
                      </Typography>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <Controller
                control={control}
                name='purchaseInvoiceIds'
                rules={{
                  validate: value => {
                    if ((!value || value.length === 0) && typeWatch === 'PURCHASE') {
                      return 'Wajib diisi.'
                    }
                    return true
                  }
                }}
                render={({ fieldState: { error } }) => (
                  <div className='flex flex-col items-center justify-center text-center'>
                    <Typography>Belum ada faktur yang dipilih</Typography>
                    <FormHelperText error={!!error}>{error?.message}</FormHelperText>
                  </div>
                )}
              />
            )}
            {selectedInvoice.length > 0 && (
              <div className='flex flex-col gap-1 rounded-md p-3 bg-[#DBF7E8]'>
                <Typography color='black'>Total Pembayaran</Typography>
                <Typography color='black' className='font-semibold'>
                  {toCurrency(selectedInvoice.reduce((acc, item) => acc + item.totalAmount, 0))}
                </Typography>
              </div>
            )}
          </CardContent>
        </Card>

        {openAddPurchaseInvoice && (
          <AddPurchaseInvoiceDialog
            open={openAddPurchaseInvoice}
            setOpen={setOpenAddPurchaseInvoice}
            selectedInvoices={selectedInvoice}
            onSubmit={invoices => {
              if (invoices) {
                setSelectedInvoice(invoices)
                setValue(
                  'purchaseInvoiceIds',
                  invoices.map(i => i.id)
                )
              }
            }}
          />
        )}
      </>
    )
  }
}

export default ItemListCard
