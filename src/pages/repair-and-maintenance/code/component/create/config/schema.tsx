import * as z from 'zod'

export const objectComponentSchema = z.object({
  code: z
    .string({ message: 'Wajib diisi' })
    .min(1, { message: 'Wajib diisi' })
    .regex(/^[^\s]+$/, { message: 'Spasi tidak diperbolehkan' }),
  description: z.string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
  family: z
    .array(z.string({ message: 'Wajib Diisi' }).min(1, { message: 'Wajib Diisi' }))
    .min(1, { message: 'Wajib Diisi' }),
  createdAt: z.string().optional().nullable(),
  id: z.string().optional().nullable()
})

export const createComponentSchema = z.object({
  items: z.array(objectComponentSchema).min(1, { message: 'Minimal 1 item component' })
})

export type ComponentType = Required<z.TypeOf<typeof createComponentSchema>>
export type ObjectComponentType = Required<z.TypeOf<typeof objectComponentSchema>>
