// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { Button, Typography } from '@mui/material'

import { FormProvider, useForm, useWatch } from 'react-hook-form'

import { useRouter } from '@/routes/hooks'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import { createPurchaseInvoiceSchemaDto, PurchaseInvoiceDtoType } from './config/schema'
import InvoiceDetail from './component/InvoiceDetail'
import Document from './component/AttachmentDocument'
import PurchaseOrders from './component/PurchaseOrders'
import OtherExpenses from './component/OtherExpenses'
import InvoiceDiscount from './component/InvoiceDiscount'
import { PurchaseInvoiceDiscountType, PurchaseInvoicePayload } from '@/types/purchaseInvoiceTypes'
import InvoiceSummary from './component/InvoiceSummary'
import ApprovalListCard from '@/pages/material-request/create/components/ApprovalListCard'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { useQuery } from '@tanstack/react-query'
import { useAuth } from '@/contexts/AuthContext'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useCreatePurchaseInvoice } from '@/api/services/purchase-invoice/mutation'
import { toast } from 'react-toastify'
import { useUploadDocument } from '@/api/services/file/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { format } from 'date-fns'
import { ApproverType } from '@/types/userTypes'
import { useEffect } from 'react'
import { useDraft } from '@/pages/draft/context/DraftContext'
import { DraftScope } from '@/types/draftsTypes'
import { DRAFT_QUERY_KEY } from '@/api/services/draft/service'
import DraftQueryMethods from '@/api/services/draft/query'
import { useSearchParams } from 'react-router-dom'
import Permission from '@/core/components/Permission'

// Transform form data to API payload
const transformFormDataToPayload = (
  formData: PurchaseInvoiceDtoType,
  approverList: ApproverType[]
): PurchaseInvoicePayload => {
  return {
    vendorId: formData.vendorId,
    paymentTerms: formData.paymentTerms,
    paymentDueDays: formData.paymentDueDays,
    paymentDueDate: formData.paymentDueDate,
    vendorNumber: formData.vendorNumber || undefined,
    vendorName: formData.vendorName || undefined,
    invoiceDate: formData.invoiceDate,
    note: formData.note || undefined,
    isDownPayment: formData.isDownPayment || false,
    currencyId: formData.currencyId,
    exchangeRate: formData.exchangeRate,
    documentUploadId: formData.documentUploadId || undefined,
    isGeneralPurchase: formData.isGeneralPurchase,
    approvals: approverList
      .filter(approval => approval.userId)
      .map(approval => ({
        userId: approval.userId!
      })),
    discountType: formData.discountType || PurchaseInvoiceDiscountType.PERCENTAGE,
    discountValue: formData.discountValue || 0,
    taxInvoiceNumber: formData.taxInvoiceNumber,
    taxInvoiceDate: formData.taxInvoiceDate,
    orders: formData.orders
      .filter(order => order.purchaseOrderId)
      .map(order => ({
        purchaseOrderId: order.purchaseOrderId!,
        incomingMaterialId: order.incomingMaterialId!,
        items: (order.items || [])
          .filter(item => item.incomingMaterialItemId && item.pricePerUnit && item.quantity)
          .map(item => ({
            incomingMaterialItemId: item.incomingMaterialItemId!,
            pricePerUnit: item.pricePerUnit!,
            quantity: item.quantity!
          })),
        totalAmount: order.totalAmount,
        downPaymentIds: order.downPaymentIds
      })) as any,
    otherExpenses: (formData.otherExpenses || [])
      .filter(expense => expense.accountId && expense.amount)
      .map(expense => ({
        accountId: expense.accountId!,
        siteId: expense.siteId!,
        departmentId: expense.departmentId!,
        amount: expense.amount!,
        note: expense.note
      })),
    departmentId: formData.departmentId,
    siteId: formData.siteId,
    projectId: formData.projectId || undefined,
    projectLabelId: formData.projectLabelId || undefined
  }
}

const CreatePurchaseInvoicePage = () => {
  const router = useRouter()
  const { userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const { createDraft, updateDraft, deleteDraft, loadingDraft } = useDraft()
  const [searchParams] = useSearchParams()

  // Purchase Invoice Mutation
  const { mutateAsync: createPurchaseInvoice, isLoading: isCreating } = useCreatePurchaseInvoice()
  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadDocument()

  const method = useForm<PurchaseInvoiceDtoType>({
    resolver: zodResolver(createPurchaseInvoiceSchemaDto),
    mode: 'onChange',
    defaultValues: {
      discountType: PurchaseInvoiceDiscountType.PERCENTAGE,
      discountValue: 0,
      paymentDueDays: 0,
      isGeneralPurchase: false,
      isDownPayment: false
    }
  })
  const { control, handleSubmit, getValues, reset } = method

  const grandTotalWatch = useWatch({
    control,
    name: 'grandTotal',
    defaultValue: 0
  })

  const siteId = useWatch({
    control,
    name: 'siteId',
    defaultValue: ''
  })

  const departmentId = useWatch({
    control,
    name: 'departmentId',
    defaultValue: ''
  })

  const { data: draftData } = useQuery({
    enabled: !!searchParams.get('draft'),
    queryKey: [DRAFT_QUERY_KEY, searchParams.get('draft')],
    queryFn: () => DraftQueryMethods.getOneDraft(searchParams.get('draft')),
    cacheTime: 0
  })

  useEffect(() => {
    if (draftData) {
      try {
        const parsed = JSON.parse(draftData.payload) as any
        const normalized = {
          ...parsed,
          // Backward compatibility: older drafts might use `paymentMethod`
          paymentTerms: parsed?.paymentTerms ?? parsed?.paymentMethod ?? ''
        } as PurchaseInvoiceDtoType
        reset(normalized)
      } catch (e) {
        // ignore parse error
      }
    }
  }, [draftData])

  const { data: approverList } = useQuery({
    enabled: !!siteId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, DefaultApprovalScope.PurchaseInvoice, siteId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        divisionId: 'null',
        scope: DefaultApprovalScope.PurchaseInvoice,
        siteId,
        departmentId: 'null'
        // departmentId
      }),
    placeholderData: []
  })

  const onSaveDraft = () => {
    const formData = getValues()
    setConfirmState({
      open: true,
      title: 'Simpan Draft Faktur Pembelian',
      content:
        'Apakah kamu yakin akan menyimpan draft Faktur Pembelian ini? Pastikan semua detil yang kamu masukkan sudah benar',
      confirmText: 'Simpan',
      onConfirm: async () => {
        try {
          let draftForm = { ...formData }
          if (draftForm.documentContent && draftForm.documentName) {
            const uploadResponse = await uploadMutate({
              fieldName: `purchase_invoice_document${format(new Date(), 'yyyyMMddHHmmss')}`,
              file: draftForm.documentContent,
              scope: 'public-document',
              fileName: draftForm.documentName
            })
            draftForm = {
              ...draftForm,
              documentUploadId: uploadResponse.data?.id,
              documentContent: undefined as any
            }
          }

          const stringifyPayload = JSON.stringify(draftForm)
          if (draftData) {
            updateDraft(
              {
                draftId: draftData.id,
                payload: stringifyPayload,
                siteId: draftForm.siteId ?? undefined
              },
              {
                onSuccess: () => {
                  toast.success('Faktur Pembelian disimpan sebagai draft.')
                  router.replace('/purchase-invoice/draft')
                }
              }
            )
          } else {
            createDraft(
              {
                scope: DraftScope['PURCHASE-INVOICE'],
                payload: stringifyPayload,
                siteId: draftForm.siteId ?? undefined
              },
              {
                onSuccess: () => {
                  toast.success('Faktur Pembelian disimpan sebagai draft.')
                  router.replace('/purchase-invoice/draft')
                }
              }
            )
          }
        } catch (error: any) {
          console.error('Error saving draft:', error)
          toast.error(error?.response?.data?.message || 'Gagal menyimpan draft')
        }
      }
    })
  }

  // Form submission handler
  const onSubmit = async (formData: PurchaseInvoiceDtoType) => {
    if ((approverList?.length ?? 0) <= 0) {
      toast.error('Default Approval belum tersedia. Silahkan hubungi admin terlebih dahulu.')
      return
    }
    setConfirmState({
      open: true,
      title: 'Buat Faktur Pembelian',
      content:
        'Apakah kamu yakin akan membuat Faktur Pembelian ini? Pastikan semua detil yang kamu masukkan untuk Faktur Pembelian ini sudah benar',
      confirmText: 'Buat Faktur',
      onConfirm: async () => {
        try {
          // Check if document needs to be uploaded first
          if (formData.documentContent && formData.documentName) {
            // Upload document first
            const uploadResponse = await uploadMutate({
              fieldName: `purchase_invoice_document${format(new Date(), 'yyyyMMddHHmmss')}`,
              file: formData.documentContent,
              scope: 'public-document',
              fileName: formData.documentName
            })

            // Update form data with uploadId
            const updatedFormData = {
              ...formData,
              documentUploadId: uploadResponse.data?.id
            }

            const payload = transformFormDataToPayload(updatedFormData, approverList)
            const response = await createPurchaseInvoice(payload)

            toast.success('Faktur pembelian berhasil dibuat!')
            router.replace(`/purchase-invoice/list/${response.data.id}`)
            if (draftData) deleteDraft(draftData.id)
          } else {
            // No document to upload, proceed directly
            const payload = transformFormDataToPayload(formData, approverList)
            const response = await createPurchaseInvoice(payload)

            toast.success('Faktur pembelian berhasil dibuat!')
            router.replace(`/purchase-invoice/list/${response.data.id}`)
            if (draftData) deleteDraft(draftData.id)
          }
        } catch (error: any) {
          console.error('Error creating purchase invoice:', error)
          toast.error(error?.response?.data?.message || 'Gagal membuat faktur pembelian')
        }
      }
    })
  }

  return (
    <FormProvider {...method}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <div className='flex flex-col md:flex-row justify-between md:items-end gap-4'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Buat Faktur Pembelian</Typography>
              <Typography>Buat faktur pembelian baru</Typography>
            </div>
            <div className='flex gap-2'>
              <Button
                color='secondary'
                variant='outlined'
                className='is-full sm:is-auto'
                onClick={() => router.replace(`/purchase-invoice/list`)}
              >
                Batalkan
              </Button>
              <LoadingButton
                className='is-full sm:is-auto'
                startIcon={<></>}
                variant='outlined'
                loading={loadingDraft}
                onClick={onSaveDraft}
              >
                Simpan Draft
              </LoadingButton>
              <Permission permission='purchase-invoice.create'>
                <LoadingButton
                  startIcon={<></>}
                  variant='contained'
                  className='is-full sm:is-auto'
                  loading={isCreating || uploadLoading}
                  onClick={handleSubmit(onSubmit, errors => {
                    console.error(errors)
                  })}
                >
                  Buat Faktur
                </LoadingButton>
              </Permission>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Grid container spacing={4}>
                <Grid item xs={12}>
                  <InvoiceDetail />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} md={6}>
              <Grid container spacing={4}>
                <Grid item xs={12}>
                  <Document />
                </Grid>
                {approverList?.length > 0 && (
                  <Grid item xs={12}>
                    <ApprovalListCard
                      approverList={
                        approverList?.map(approver => ({ ...approver.user, threshold: approver.threshold ?? 0 })) ?? []
                      }
                      scope={DefaultApprovalScope.PurchaseInvoice}
                      amount={grandTotalWatch}
                    />
                  </Grid>
                )}
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <PurchaseOrders />
            </Grid>
            {/* <Grid item xs={12}>
              <InvoiceDiscount />
            </Grid> */}
            <Grid item xs={12}>
              <OtherExpenses />
            </Grid>
            <Grid item xs={12}>
              <InvoiceSummary />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default CreatePurchaseInvoicePage
