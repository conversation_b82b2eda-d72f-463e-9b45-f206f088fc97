import {
  Card,
  CardContent,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  InputLabel,
  ListItemText,
  ListSubheader,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { useSm } from '../../context/SmContext'
import { useAuth } from '@/contexts/AuthContext'
import { StockMovementPayload, StockMovementPurpose } from '../../config/type'
import { ListResponse } from '@/types/api'
import { SiteType } from '@/types/companyTypes'
import { defaultListData } from '@/api/queryClient'
import CompanyQueryMethods, { SITE_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'

const AddSmDataCard = () => {
  const { userProfile, ownSiteList, groupedSiteList } = useAuth()
  const { showLoading, setPartialSmParams, setSelectedSiteId, siteList } = useSm()
  const {
    control,
    setValue,
    resetField,
    formState: { errors }
  } = useFormContext<StockMovementPayload>()

  const poIdWatch = useWatch({
    control,
    name: 'purchaseOrderId',
    defaultValue: ''
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Data Pindah Barang</Typography>
        </div>
        <Controller
          name='originSiteId'
          control={control}
          rules={{ required: true }}
          render={({ field }) => (
            <>
              <FormControl>
                <InputLabel id='role-select'>Pilih Gudang Asal</InputLabel>
                <Select
                  {...field}
                  key={field.value}
                  fullWidth
                  id='select-siteId'
                  onChange={e => {
                    const value = e.target.value
                    setPartialSmParams('siteId', value)
                    setSelectedSiteId(value)
                    field.onChange(value)
                    if (!poIdWatch) {
                      resetField('destinationSiteId')
                    }
                  }}
                  label='Pilih Gudang Asal'
                  labelId='siteId-select'
                  inputProps={{ placeholder: 'Pilih Lokasi' }}
                  disabled={!!poIdWatch}
                  defaultValue=''
                >
                  {ownSiteList?.map(site => (
                    <MenuItem key={site.id} value={site.id}>
                      {site.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Controller
                name='destinationSiteId'
                control={control}
                rules={{ required: true }}
                render={({ field: destinationSiteField, fieldState: { error } }) => (
                  <FormControl>
                    <InputLabel id='role-select'>Pilih Gudang Tujuan</InputLabel>
                    <Select
                      {...destinationSiteField}
                      key={destinationSiteField.value}
                      fullWidth
                      id='select-siteId'
                      label='Pilih Gudang Tujuan'
                      labelId='siteId-select'
                      inputProps={{ placeholder: 'Pilih Lokasi' }}
                      disabled={!!poIdWatch}
                      defaultValue=''
                    >
                      {groupedSiteList.map(group => {
                        let children = []
                        children.push(
                          <ListSubheader
                            className='bg-green-50 text-primary font-semibold'
                            key={group.projectId ?? 'no_project'}
                          >
                            {group.project?.name || 'Tanpa Proyek'}
                          </ListSubheader>
                        )
                        group.sites
                          ?.filter(site => site.id !== field.value)
                          ?.forEach(site => {
                            children.push(
                              <MenuItem key={site.id} value={site.id}>
                                <ListItemText primary={site.name} />
                              </MenuItem>
                            )
                          })
                        return children
                      })}
                    </Select>
                    {!!error?.message && (
                      <FormHelperText className='mt-3 ml-0' error>
                        Gudang tujuan wajib dipilih
                      </FormHelperText>
                    )}
                  </FormControl>
                )}
              />
            </>
          )}
        />
        <Controller
          name='purpose'
          control={control}
          render={({ field: { onChange, value } }) => (
            <FormControlLabel
              control={
                <Checkbox
                  key={JSON.stringify(value)}
                  checked={value === StockMovementPurpose.FOR_RMA}
                  onChange={e =>
                    onChange(e.target.checked ? StockMovementPurpose.FOR_RMA : StockMovementPurpose.NORMAL_MOVE)
                  }
                />
              }
              label='Untuk RMA'
            />
          )}
        />
        <Controller
          name='note'
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              disabled={showLoading}
              fullWidth
              label='Catatan (optional)'
              variant='outlined'
              placeholder='Contoh: Surplus stok'
              className='mbe-5'
              {...(errors.note && { error: true, helperText: errors.note.message })}
            />
          )}
        />
      </CardContent>
    </Card>
  )
}

export default AddSmDataCard
