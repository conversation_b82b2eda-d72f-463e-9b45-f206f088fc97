// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import { Link } from 'react-router-dom'
import { MrType } from '@/types/mrTypes'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

const MrNumberCard = ({ mrData }: { mrData?: MrType }) => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-1'>
        <Typography>Dibuat dari Material Request</Typography>
        <div className='flex justify-between items-center'>
          <Link to={`/mr-in/${mrData?.id}`}>
            <Typography className='underline' variant='h5'>
              No. MR: {mrData?.number}
            </Typography>
          </Link>
        </div>
        <Typography>{formatDate(mrData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}</Typography>
      </CardContent>
    </Card>
  )
}

export default MrNumberCard
