import { useEffect } from 'react'
import { FormControl, InputLabel, ListItemText, ListSubheader, MenuItem, Select, Typography } from '@mui/material'
import Card from '@mui/material/Card'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { tableColumns } from '../config/table'
import { useItem } from '../context/ItemContext'
import { useRouter } from '@/routes/hooks'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { useAuth } from '@/contexts/AuthContext'

const MgStockList = () => {
  const router = useRouter()
  const { userProfile, groupedSiteList } = useAuth()
  const {
    itemListResponse: { items: itemList, totalItems, totalPages },
    itemsParams: { page, search, categoryId, siteId, startDate, endDate, limit, isInStock },
    setItemsParams,
    setSelectedItemId,
    setPartialItemsParams,
    categoryList,
    sites
  } = useItem()
  const warehouseGroupedSiteList = groupedSiteList
    .map(group => ({
      ...group,
      sites: group.sites
    }))
    .filter(group => group.sites?.length > 0)

  // TODO: MOVE THIS SHIT
  const table = useReactTable({
    data: itemList,
    columns: tableColumns(
      {
        showDetail: id => {
          setSelectedItemId(id)
          router.push(`/mg/stock/${id}`)
        }
      },
      siteId
    ),
    initialState: {
      pagination: {
        pageSize: limit ?? 10,
        pageIndex: page - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const filterGroupConfig: FilterGroupConfig = {
    date: {
      options: [],
      values: [startDate, endDate]
    },
    category: {
      options: categoryList.map(category => {
        return { value: category.id, label: category.name }
      }),
      values: categoryId ? [categoryId] : []
    }
  }

  const onFilterChanged = ({ date, status, priority, category }: FilterValues) => {
    setItemsParams(prev => {
      return {
        ...prev,
        page: 1,
        startDate: date[0],
        endDate: date[1],
        status: status?.length > 0 ? status[0] : undefined,
        priority: priority?.length > 0 ? priority[0] : undefined,
        categoryId: category?.length > 0 ? category[0] : undefined
      }
    })
  }

  useEffect(() => {
    if ((sites?.length ?? 0) > 0) {
      setItemsParams(current => ({
        ...current,
        limit: 10,
        page: 1,
        siteId: 'all'
      }))
    }
    setSelectedItemId(undefined)
  }, [sites])

  useEffect(() => {
    if (siteId === 'all') {
      setItemsParams(current => ({
        ...current,
        siteIds: warehouseGroupedSiteList
          .map(group => group.sites)
          .flat()
          .map(site => site.id)
          .join(',')
      }))
    }
  }, [siteId])

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setItemsParams(prev => ({ ...prev, page: 1, search: value as string }))}
            placeholder='Cari Barang'
            className='is-full sm:is-auto'
          />
          <FormControl size='small' className='w-[220px] max-sm:is-full'>
            <InputLabel id='role-select'>Pilih Lokasi</InputLabel>
            <Select
              key={siteId}
              fullWidth
              id='select-siteId'
              value={siteId}
              onChange={e => setPartialItemsParams('siteId', e.target.value)}
              label='Pilih Lokasi'
              size='small'
              labelId='siteId-select'
              inputProps={{ placeholder: 'Pilih Lokasi' }}
              defaultValue=''
            >
              <MenuItem value='all'>Semua Lokasi</MenuItem>
              {warehouseGroupedSiteList.map(group => {
                let children = []
                children.push(
                  <ListSubheader
                    className='bg-green-50 text-primary font-semibold'
                    key={group.projectId ?? 'no_project'}
                  >
                    {group.project?.name || 'Tanpa Proyek'}
                  </ListSubheader>
                )
                group.sites.forEach(site => {
                  children.push(
                    <MenuItem key={site.id} value={site.id}>
                      <ListItemText primary={site.name} />
                    </MenuItem>
                  )
                })
                return children
              })}
            </Select>
          </FormControl>
          <FormControl size='small' className='w-[220px] max-sm:is-full'>
            <InputLabel id='is-in-stock'>Ketersediaan Stok</InputLabel>
            <Select
              key={JSON.stringify(isInStock)}
              fullWidth
              id='select-is-in-stock'
              value={isInStock === undefined ? '' : isInStock ? 'true' : 'false'}
              onChange={e => {
                const value = e.target.value
                setPartialItemsParams('isInStock', value === '' ? undefined : value === 'true')
              }}
              label='Ketersediaan Stok'
              size='small'
              labelId='is-in-stock'
              inputProps={{ placeholder: 'Pilih Ketersediaan Stok' }}
              defaultValue=''
            >
              <MenuItem value=''>Semua</MenuItem>
              <MenuItem value='true'>Stok Tersedia</MenuItem>
              <MenuItem value='false'>Stok Tidak Tersedia</MenuItem>
            </Select>
          </FormControl>
          <FilterGroupDialog config={filterGroupConfig} onFilterApplied={onFilterChanged} />
          {/* key=siteId supaya reconcile perubahan prop untuk default filter lokasi*/}
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography> Belum ada Barang</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua stok barang di gudang akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setItemsParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialItemsParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setItemsParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialItemsParams('page', pageIndex)}
      />
    </Card>
  )
}

export default MgStockList
