// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import TimelineDot from '@mui/lab/TimelineDot'
import TimelineItem from '@mui/lab/TimelineItem'
import TimelineContent from '@mui/lab/TimelineContent'
import TimelineSeparator from '@mui/lab/TimelineSeparator'
import TimelineConnector from '@mui/lab/TimelineConnector'
import Typography from '@mui/material/Typography'

import { Timeline } from '@/components/Timeline'
import { Avatar, FormControl, InputLabel, ListItemText, ListSubheader, MenuItem, Select } from '@mui/material'
import { formatDistanceToNow } from 'date-fns'
import { id } from 'date-fns/locale'
import { useItem } from '../../context/ItemContext'
import Separator from '@/components/Separator'
import { ItemStockLogType } from '../../config/utils'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect } from 'react'

const StockLogCard = () => {
  const { userProfile, groupedSiteList } = useAuth()
  const {
    stockLogList,
    itemData,
    setLogsParams,
    setPartialLogsParams,
    logsParams: { siteIds, siteId },
    itemsParams: { siteId: itemSiteId },
    setPartialItemsParams
  } = useItem()
  const warehouseGroupedSiteList = groupedSiteList
    .map(group => ({
      ...group,
      sites: group.sites
    }))
    .filter(group => group.sites?.length > 0)

  const stockCount =
    (!siteIds || siteIds?.length <= 0 || siteIds?.length > 1
      ? itemData?.stock
      : itemData?.stocks?.filter(stock => stock.siteId === siteIds?.[0])?.[0]?.totalStock) ?? 0

  useEffect(() => {
    setPartialLogsParams(
      'siteIds',
      !itemSiteId || itemSiteId === 'all'
        ? warehouseGroupedSiteList?.flatMap(group => group.sites?.map(site => site.id))?.join(',')
        : [itemSiteId]
    )
  }, [])

  useEffect(() => {
    if (siteId) {
      setPartialItemsParams('siteId', siteId)
    }
  }, [siteId])

  return (
    <Card className='max-h-[640px] overflow-auto'>
      <CardHeader
        title='Log Mutasi Barang'
        component={() => (
          <div className='p-5 pb-0 flex flex-col gap-4 sticky top-0 bg-white dark:bg-inherit z-10'>
            <div className='flex justify-between'>
              <Typography variant='h5'>Log Mutasi Barang</Typography>
              {userProfile && (
                <FormControl size='small'>
                  <InputLabel id='role-select' shrink>
                    Pilih Lokasi
                  </InputLabel>
                  <Select
                    key={siteId}
                    id='select-siteId'
                    value={siteId}
                    onChange={e => {
                      setPartialLogsParams('siteId', e.target.value)
                      setPartialLogsParams(
                        'siteIds',
                        !e.target.value || e.target.value === 'all'
                          ? warehouseGroupedSiteList?.flatMap(group => group.sites?.map(site => site.id))?.join(',')
                          : [e.target.value]
                      )
                    }}
                    label='Pilih Lokasi'
                    size='small'
                    labelId='siteId-select'
                    inputProps={{ placeholder: 'Pilih Lokasi' }}
                    displayEmpty
                    defaultValue={itemSiteId || 'all'}
                  >
                    <MenuItem value='all'>Semua Lokasi</MenuItem>
                    {warehouseGroupedSiteList.map(group => {
                      let children = []
                      children.push(
                        <ListSubheader
                          className='bg-green-50 text-primary font-semibold'
                          key={group.projectId ?? 'no_project'}
                        >
                          {group.project?.name || 'Tanpa Proyek'}
                        </ListSubheader>
                      )
                      group.sites.forEach(site => {
                        children.push(
                          <MenuItem key={site.id} value={site.id}>
                            <ListItemText primary={site.name} />
                          </MenuItem>
                        )
                      })
                      return children
                    })}
                  </Select>
                </FormControl>
              )}
            </div>
            <div className='flex flex-col gap-1'>
              <Typography variant='caption'>Total Stok</Typography>
              <Typography>
                {stockCount} {itemData?.smallUnit}
              </Typography>
              <Separator containerClassName='mt-1 mb-0 pb-0' />
            </div>
          </div>
        )}
      />
      <CardContent>
        <div className='flex flex-col'>
          <Timeline>
            {stockLogList?.map(log => {
              let title = ''
              let color = 'primary'
              switch (log.type) {
                case ItemStockLogType.IN:
                  title = 'Barang Masuk'
                  break
                case ItemStockLogType.OUT:
                  {
                    if (log.description.includes('RMA')) {
                      title = 'Barang Dikembalikan'
                    } else {
                      title = 'Barang Keluar'
                    }
                    color = 'error'
                  }
                  break
                case ItemStockLogType.ADJUSTMENT:
                  title = 'Penyesuaian Stok'
                  if (log.quantity < 0) {
                    color = 'error'
                  }
                  break
                case ItemStockLogType.NON_TRX:
                  title = 'Penyesuaian Data'
                  break
              }
              return (
                <>
                  <TimelineItem key={log.id} className='pt-2'>
                    <TimelineSeparator>
                      <TimelineDot color={color as any} />
                      <TimelineConnector />
                    </TimelineSeparator>
                    <TimelineContent>
                      <div className='flex flex-wrap items-center justify-between gap-x-2 mbe-1'>
                        <Typography color='text.primary' className='font-medium text-base'>
                          {title}
                        </Typography>
                        <Typography variant='caption'>
                          {log.createdAt
                            ? formatDistanceToNow(log.createdAt, {
                                locale: id,
                                addSuffix: true
                              })
                                .replace('sekitar ', '')
                                .replace('kurang dari ', '')
                            : ''}
                        </Typography>
                      </div>
                      <Typography variant='subtitle1'>{log.site?.name}</Typography>
                      <div className='flex flex-col'>
                        {log?.type !== ItemStockLogType.NON_TRX && (
                          <Typography variant='button'>
                            {log.quantity} {log.quantityUnit}
                          </Typography>
                        )}
                        <Typography>
                          {log?.description
                            ?.replaceAll('"', '')
                            .replaceAll('Location', 'Lokasi')
                            .replaceAll('changed to', '→')
                            .replaceAll('{added}', '')}
                        </Typography>
                      </div>
                      {log.approvedByuser ? (
                        <div className='flex items-center gap-3 mt-2'>
                          <Avatar />
                          <div className='flex flex-col'>
                            <Typography variant='caption'>Disetujui oleh</Typography>
                            <Typography color='text.primary' className='font-medium'>
                              {log.approvedByuser?.fullName}
                            </Typography>
                            <Typography variant='body2'>{log.approvedByuser?.title}</Typography>
                          </div>
                        </div>
                      ) : null}
                      {log.user ? (
                        <div className='flex items-center gap-3 mt-2'>
                          <Avatar />
                          <div className='flex flex-col'>
                            {log.type === ItemStockLogType.IN || log.type === ItemStockLogType.OUT ? (
                              <Typography variant='caption'>
                                {log.type === ItemStockLogType.IN ? 'Diterima oleh' : 'Diambil oleh'}
                              </Typography>
                            ) : (
                              <Typography variant='caption'>Diubah oleh</Typography>
                            )}
                            <Typography color='text.primary' className='font-medium'>
                              {log.user?.fullName}
                            </Typography>
                            <Typography variant='body2'>{log.user?.title}</Typography>
                          </div>
                        </div>
                      ) : null}
                    </TimelineContent>
                  </TimelineItem>
                </>
              )
            })}
          </Timeline>
        </div>
      </CardContent>
    </Card>
  )
}

export default StockLogCard
