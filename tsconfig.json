{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2021", "DOM", "DOM.Iterable"], "baseUrl": ".", "allowJs": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "types": ["web-app-manifest"], "allowImportingTsExtensions": true, "paths": {"@/*": ["./src/*"], "@/core/*": ["./src/core/*"], "@layouts/*": ["src/layouts/*"], "@menu/*": ["src/components/menu/*"], "@assets/*": ["./src/assets/*"], "@/components/*": ["./src/components/*"], "@/configs/*": ["./src/configs/*"]}}, "include": ["tailwind.config.ts", "src", "vite-env.d.ts"], "exclude": ["node_modules"], "rules": {"no-unused-vars": "off"}, "references": [{"path": "./tsconfig.node.json"}]}